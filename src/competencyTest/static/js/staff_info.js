import { dynamicUnitDropdown } from "./utils.js";

function getDataAttributes() {
    const dataContainer = document.getElementById('data-container');
    const unitsByDepartment = JSON.parse(dataContainer.dataset.unitsByDepartment);
    const currentUnit = dataContainer.dataset.currentUnit;
    return { unitsByDepartment, currentUnit };
}

function initialize() {
    const { unitsByDepartment, currentUnit } = getDataAttributes();
    
    // Set up dynamic unit dropdown
    dynamicUnitDropdown('department', 'unit', unitsByDepartment);
    
    // Set the current unit after the dropdown is populated
    setTimeout(() => {
        const unitSelect = document.getElementById('unit');
        if (unitSelect && currentUnit) {
            unitSelect.value = currentUnit;
        }
    }, 100);
    
    // Add form validation
    setupFormValidation();
}

function setupFormValidation() {
    const form = document.querySelector('form');
    const nameInput = document.getElementById('name');
    const departmentSelect = document.getElementById('department');
    const unitSelect = document.getElementById('unit');

    form.addEventListener('submit', function(e) {
        e.preventDefault(); // Always prevent default submission

        // Validate name
        if (!nameInput.value.trim()) {
            alert('Please enter a name.');
            nameInput.focus();
            return false;
        }

        // Validate department and unit
        if (!departmentSelect.value || !unitSelect.value) {
            alert('Please select both department and unit.');
            return false;
        }

        // Show confirmation modal
        showConfirmationModal(nameInput.value.trim(), departmentSelect.value, unitSelect.value);
    });
}

function showConfirmationModal(name, department, unit) {
    // Update modal content
    document.getElementById('confirmName').textContent = name;
    document.getElementById('confirmDepartment').textContent = department;
    document.getElementById('confirmUnit').textContent = unit;

    // Show the modal
    const confirmModal = new bootstrap.Modal(document.getElementById('confirmationModal'));
    confirmModal.show();
}

function confirmUpdate() {
    // Submit the form
    document.querySelector('form').submit();
}

// Make confirmUpdate globally accessible
window.confirmUpdate = confirmUpdate;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initialize);
