from flask import render_template, request, redirect, url_for, session, flash
from datetime import datetime
import firebase_admin as fb
from firebase_admin import auth as fire_auth
from firebase_admin import credentials

from competencyTest import app, pyre_auth, db
from competencyTest.config import *
from competencyTest.project_utils import *

# from openpyxl import load_workbook
# from openpyxl.styles import Font, Border, Side, Alignment
# import pandas as pd
import pytz


@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('ui'))
    return redirect(url_for('login'))


@app.route('/ui', methods=['GET','POST'])
def ui():
    if 'user_id' not in session:
        return redirect(url_for('index'))
    
    user_name = session.get('user_name')
    
    return render_template(
        'ui.html', 
        basic_app_config=BASIC_APP_CONFIG,
        user_name=user_name
    )


@app.route('/view', methods=['GET','POST'])
def view():

    if 'user_id' not in session:
        return redirect(url_for('index'))

    allowed_departments, units_by_department = get_dept_and_ward(session)


    units_by_department = {
        key:['ALL'] + value 
        for key, value in units_by_department.items()
    }

    tests_overview = get_tests_overview()
    
    if request.method == 'POST':

        test = request.form.get('test')
        version = request.form.get('version')

        if 'submit-u' in request.form:
            dept = request.form.get('department')
            unit = request.form.get('unit')
            return redirect(
                url_for(
                    'result',
                    query_by='unit',
                    dept=dept,
                    unit=unit,
                    test=test,
                    version=version
                )
            )
        
        if 'submit-p' in request.form:
            employee_no = request.form.get('employee-no')
            name = request.form.get('name').strip().title()

            if not employee_no and not name:
                form_status = {
                    'u': ['',''],
                    'p': ['active','show active'],
                }
                flash('Please fill in at least one of the fields: Employee No, Name','danger')
                return render_template(
                    'view.html',
                    basic_app_config=BASIC_APP_CONFIG,
                    allowed_departments=allowed_departments, 
                    units_by_department=units_by_department,
                    form_status=form_status
                )

            return redirect(
                url_for(
                    'result',
                    query_by='person',
                    employee_no=employee_no,
                    name=name,
                    test=test,
                    version=version
                )
            )


    form_status = {
        'u': ['active','show active'],
        'p': ['','']
    }
    

    return render_template(
        'view.html',
        basic_app_config=BASIC_APP_CONFIG,
        allowed_departments=allowed_departments, 
        units_by_department=units_by_department,
        form_status=form_status,
        tests_overview=tests_overview
    )


@app.route('/result', methods=['GET','POST'])
def result():

    if 'user_id' not in session:
        return redirect(url_for('index'))

    if request.method == 'GET':

        args = request.args.to_dict()
        query_test = args.get('test','')
        query_ver = args.get('version','')
        query_by = args.get('query_by','')

        if not (query_test and query_ver):
            flash('Please select a test and version.', 'danger')
            return redirect(url_for('view'))


        try:
            data = (
                list(db.child('pca').get().val().values() if db.child('pca').get().val() else [])
                + list(db.child('nurse').get().val().values() if db.child('nurse').get().val() else [])
                + list(db.child('temp_staff').get().val().values() if db.child('temp_staff').get().val() else [])
            )
        except:
            print('Error getting data from database "Nurse" or "PCA" or "Temp Staff"')
            data = []
        
        if not data:
            return render_template(
                'result.html',
                basic_app_config=BASIC_APP_CONFIG,
                result_data=[{
                    'Unit': '[ NIL ]',
                    'Name': '[ NIL ]',
                    'Employee_No': '[ NIL ]',
                    'Result': '[ NIL ]' # if query_test != 'ALL' else ['[ NIL ]']
                }],
                dept=args.get('dept','ANY'),
                test_form={},
                view_result='View Staff',
                query_test=query_test,
                query_by=query_by
            )
        

        if query_by == 'unit':
            dept = args.get('dept','')
            unit = args.get('unit','')
            if unit == 'ALL':
                if dept == 'ALL':
                    filtered_data = list(data)
                else:
                    filtered_data = [
                        d for d in data 
                        if d['Department'] == dept
                    ]
            else:
                filtered_data = [
                    d for d in data 
                    if (d['Department'] == dept or dept == 'ALL')
                    and d['Unit'] == unit
                ]

        elif query_by == 'person':
            employee_no = args.get('employee_no','')
            name = args.get('name','').strip().title()
            allowed_departments = get_dept_and_ward(session)[0]
            filtered_data = [
                d 
                for d in data 
                if employee_no in d['Employee_No'] 
                and name in d['Name']
                and d['Department'] in allowed_departments
                and (query_test in d['Assessments'] or query_test == 'ALL')
                and (query_ver in d['Assessments'].get(query_test,{}) or query_ver == 'ALL')
            ]

        # print(f'Filtered Data: {len(filtered_data)}')

        if not filtered_data:
            return render_template(
                'result.html',
                basic_app_config=BASIC_APP_CONFIG,
                result_data=[{
                    'Unit': '[ NIL ]',
                    'Name': '[ NIL ]',
                    'Employee_No': '[ NIL ]',
                    'Result': '[ NIL ]' # if query_test != 'ALL' else ['[ NIL ]']
                }],
                dept=dept if query_by == 'unit' else 'ANY',
                test_form={},
                view_result='View Staff',
                query_test=query_test,
                query_by=query_by
            )
        

        if query_test != 'ALL':

            test_form = dict(db.child('test').child(query_test).child(query_ver).get().val())
        
            result_data = [
                {
                    'Unit': fd['Unit'],
                    'Name': fd['Name'],
                    'Employee_No': fd['Employee_No'],
                    'Result': {
                        'test_name': query_test, 
                        'version': query_ver,
                        **fd['Assessments'][query_test][query_ver]
                    }
                }
                for fd in filtered_data
                if fd.get('Assessments',{}).get(query_test,{}).get(query_ver,'[ NIL ]') != '[ NIL ]'
            ]

        else:

            test_form = dict(db.child('test').get().val())

            result_data = [
                {
                    'Unit': fd['Unit'],
                    'Name': fd['Name'],
                    'Employee_No': fd['Employee_No'],
                    'Result': [
                        {
                            'test_name': test_name,
                            'test_title': (
                                test_form
                                .get(test_name, {})
                                .get(sorted(versions.keys(),reverse=True)[0],{})
                                .get('content',{})
                                .get('intro',{})
                                .get('header','')[:12]
                                + ' ...'
                            ),
                            'version': sorted(versions.keys(),reverse=True)[0],
                            **versions[sorted(versions.keys(),reverse=True)[0]]
                        }
                        
                        for test_name, versions in fd.get('Assessments',{}).items()
                    ]
                }
                for fd in filtered_data
            ]
            

            tests = {}
            for row in result_data:
                for test in row['Result']:
                    test_name = test['test_name']
                    ver = test['version']
                    tests[test_name] = test_form[test_name][ver]

            test_form = tests


        # print(f'Result Data: {result_data}')
        # print(f'Test Form: {test_form}')

        return render_template(
            'result.html',
            basic_app_config=BASIC_APP_CONFIG,
            result_data=result_data,
            dept=dept if query_by == 'unit' else 'ANY',
            query_test=query_test,
            test_form=test_form,
            view_result='View Staff',
            query_by=query_by
        )
    
    if request.method == 'POST':

        data = request.form

        # Handling redirection to QC Test Form
        if 'redirect-employeeNo' in data:

            test_name = data['redirect-test']

            if test_name == 'Fall_Test':
                redirect_url = 'fall_quiz_form'
            elif test_name == 'AED123':
                redirect_url = 'checklist_form'

            return redirect(
                url_for(
                    redirect_url,
                    test_name=test_name
                )
            )


@app.route('/login', methods=['GET','POST'])
def login():
    if request.method == 'POST':

        email = request.form['email']
        password = request.form['password']
        
        try:
            user = pyre_auth.sign_in_with_email_and_password(email, password)
            session['user_id'] = user['localId']
            session['user_email'] = user['email']
            session['account_info'] = pyre_auth.get_account_info(user['idToken'])['users'][0]
            permissions = dict(db.child('permission').get().val())
            name_list = [
                data['unit'] 
                for data in permissions.values()
                if data['email'] == session['user_email']
            ]
            name_list.append('Backend')
            session['user_name'] = name_list[0]

            if session['user_email'] in [data['email'] for data in ADMIN_PERMISSION]:
                session['admin'] = True
            else:
                session['admin'] = False

            return redirect(url_for('ui'))
        
        except Exception as e:
            print(e)
            flash('Invalid email or password','danger')

            return redirect(url_for('ui'))
        
    return render_template('login.html',basic_app_config=BASIC_APP_CONFIG)


@app.route('/fall-quiz-form', methods=['GET', 'POST'])
def fall_quiz_form():
    
    departments, units_by_department = get_all_depts_units()

    fall_test_form, fall_test_info = get_latest_form('Fall_Test')

    if request.method == 'POST':

        data = request.form
        employee = data['employeeno']
        fall_quiz_result = {
            k:{
                'reply': v,
                'ans': fall_test_form['questions']['details'][k]['ans'],
                'score': fall_test_info['score_per_question'] if v == fall_test_form['questions']['details'][k]['ans'] else 0
            } 
            for k,v in data.items() if k.startswith('q')
        }
        quiz_score = sum([fall_quiz_result[k]['score'] for k in fall_quiz_result])

        hong_kong_tz = pytz.timezone('Asia/Hong_Kong')
        time_now = datetime.now(hong_kong_tz).strftime("%Y-%m-%d %H:%M")

        nurses = dict(db.child('nurse').get().val())

        if employee in nurses.keys():
            new_data = nurses[employee]
            if 'Assessment' not in new_data:
                new_data['Assessments'] = {}
            if 'Fall_Test' not in new_data['Assessments']:
                new_data['Assessments']['Fall_Test'] = {}
        else:
            new_data = {
                'Department': data['department'],
                'Unit': data['unit'],
                'Name': data['name'].strip().title(),
                'Employee_No': employee.strip(),
                'Assessments': {'Fall_Test': {}}
            }
        
        new_data['Assessments']['Fall_Test'][fall_test_info['version']] = { 
            'version': fall_test_info['version'],
            'result': fall_quiz_result, 
            'score': f'{quiz_score}/{fall_test_info["total_score"]}',
            'pass': True if quiz_score >= fall_test_info['pass_score'] else False, 
            'time': time_now
        }


        db.child("nurse").child(employee).set(new_data)

        # Flash a success message
        # flash('Data added successfully!', 'success')

        return redirect(
            url_for(
                'quiz_result',
                re_test_name='fall_quiz_form',
                score=new_data['Assessments']['Fall_Test'][fall_test_info['version']]['score'],
                quiz_pass=new_data['Assessments']['Fall_Test'][fall_test_info['version']]['pass']
            )
        )


    return render_template(
        'fall-quiz-form.html',
        basic_app_config=BASIC_APP_CONFIG,
        departments=departments,
        units_by_department=units_by_department,
        fall_test_form=fall_test_form
    )

    
    
@app.route('/pca-fall-quiz-form', methods=['GET', 'POST'])
def pca_fall_quiz_form():
    
    departments, units_by_department = get_all_depts_units()

    fall_test_form, fall_test_info = get_latest_form('PCA_Fall_Test')

    if request.method == 'POST':

        data = request.form
        employee = data['employeeno']
        fall_quiz_result = {
            k:{
                'reply': v,
                'ans': fall_test_form['questions']['details'][k]['ans'],
                'score': fall_test_info['score_per_question'] if v == fall_test_form['questions']['details'][k]['ans'] else 0
            } 
            for k,v in data.items() if k.startswith('q')
        }
        quiz_score = sum([fall_quiz_result[k]['score'] for k in fall_quiz_result])

        hong_kong_tz = pytz.timezone('Asia/Hong_Kong')
        time_now = datetime.now(hong_kong_tz).strftime("%Y-%m-%d %H:%M")

        
        pcas = db.child('pca').get().val()
        if pcas is None:
            pcas = {}

        if employee in pcas.keys():
            new_data = pcas[employee]
            if 'Assessments' not in new_data:
                new_data['Assessments'] = {}
            if 'PCA_Fall_Test' not in new_data['Assessments']:
                new_data['Assessments']['PCA_Fall_Test'] = {}
        else:
            new_data = {
                'Department': data['department'],
                'Unit': data['unit'],
                'Name': data['name'].strip().title(),
                'Employee_No': employee.strip(),
                'Assessments': {'PCA_Fall_Test': {}}
            }
        
        new_data['Assessments']['PCA_Fall_Test'][fall_test_info['version']] = { 
            'version': fall_test_info['version'],
            'result': fall_quiz_result, 
            'score': f'{quiz_score}/{fall_test_info["total_score"]}',
            'pass': True if quiz_score >= fall_test_info['pass_score'] else False, 
            'time': time_now
        }


        db.child("pca").child(employee).set(new_data)

        # Flash a success message
        # flash('Data added successfully!', 'success')

        return redirect(
            url_for(
                'quiz_result',
                re_test_name='pca_fall_quiz_form',
                score=new_data['Assessments']['PCA_Fall_Test'][fall_test_info['version']]['score'],
                quiz_pass=new_data['Assessments']['PCA_Fall_Test'][fall_test_info['version']]['pass']
            )
        )


    return render_template(
        'pca-fall-quiz-form.html',
        basic_app_config=BASIC_APP_CONFIG,
        departments=departments,
        units_by_department=units_by_department,
        fall_test_form=fall_test_form
    )


@app.route('/new-mc', methods=['GET', 'POST'])
def new_mc():

    if 'user_id' not in session:
        return redirect(url_for('index'))
    
    test_name = request.args.get('test_name','')
    
    if request.method == 'POST':
        data = request.form
        ver = 'v001'
        new_mc = formulate_mc(data,ver)

        db.child('test').child(test_name.replace(' ','_')).child(ver).set(new_mc)

        flash('New MC created successfully!', 'success')

        return redirect(url_for('ui'))

    return render_template(
        'new-mc.html',
        basic_app_config=BASIC_APP_CONFIG,
        test_name=test_name
    )

@app.route('/edit-mc', methods=['GET', 'POST'])
def edit_mc():

    if 'user_id' not in session:
        return redirect(url_for('index'))
    
    fall_test_form, fall_test_info = get_latest_form('Fall_Test')

    if request.method == 'POST':
        print (request.form)
        return redirect(url_for('ui'))

    return render_template(
        'edit-mc.html',
        basic_app_config=BASIC_APP_CONFIG,
        fall_test_form=fall_test_form,
        fall_test_info=fall_test_info
    )

@app.route('/new-checklist', methods=['GET', 'POST'])
def new_checklist():

    if 'user_id' not in session:
        return redirect(url_for('index'))
    
    if request.method == 'GET':

        args = request.args
        test_name = args.get('test_name','')
        passing_criteria = db.child('passing_criteria').get().val() if db.child('passing_criteria').get().val() else {}

    if request.method == 'POST':
        
        data = request.form
        ver = 'v001'
        new_checklist = formulate_checklist(data,ver)
        test_name = data['test-name']

        db.child('test').child(test_name).child(ver).set(new_checklist)

        flash('New checklist created successfully!', 'success')

        return redirect(url_for('ui'))

    return render_template(
        'new-checklist.html',
        basic_app_config=BASIC_APP_CONFIG,
        test_name=test_name,
        passing_criteria=passing_criteria
    )

@app.route('/checklist-form', methods=['GET', 'POST'])
def checklist_form():
    if 'user_id' not in session:
        return redirect(url_for('index'))
    
    if request.method == 'GET':

        pcas = dict(db.child('pca').get().val() if db.child('pca').get().val() else {})
        nurses = dict(db.child('nurse').get().val() if db.child('nurse').get().val() else {})
        temp_staff = dict(db.child('temp_staff').get().val() if db.child('temp_staff').get().val() else {})

        test_name = request.args.get('test_name','')
        if 'PCA' in test_name and 'Fall_Test' not in test_name:
            is_pca_assessment_form = True
        else:
            is_pca_assessment_form = False

        departments, units_by_department = get_all_depts_units()
        checklist_content, checklist_info = get_latest_form(test_name)
        passing_criteria = db.child('passing_criteria').get().val() if db.child('passing_criteria').get().val() else {}
        if passing_criteria:
            passing_criteria = passing_criteria.get(checklist_info.get('passing_criteria','NA'),{}).get('content',{})

        return render_template(
            'checklist-form.html',
            basic_app_config=BASIC_APP_CONFIG,
            test_name=test_name,
            passing_criteria=passing_criteria,
            checklist_content=checklist_content,
            checklist_info=checklist_info,
            departments=departments,
            units_by_department=units_by_department,
            ranks=RANKS,
            pcas=pcas,
            nurses=nurses,
            temp_staff=temp_staff,
            is_pca_assessment_form=is_pca_assessment_form
        )
    

    else:
    # POST
        
        data = request.form
        data = {
            k:v.strip() for k,v in data.items()
        }

        print(data)

        employee = data['employeeno']
        test_name = data['test_name']
        ver = data['test_version']

        the_test = dict(db.child('test').child(test_name).child(ver).get().val())

        if test_name.startswith('PCA'):
            staffs = dict(db.child('pca').get().val())
            staff_type = 'pca'
        else:
            staffs = dict(db.child('nurse').get().val())
            staff_type = 'nurse'

        if employee in staffs.keys():
            new_data = staffs[employee]
            if 'Assessments' not in new_data:
                new_data['Assessments'] = {}
            if test_name not in new_data['Assessments']:
                new_data['Assessments'][test_name] = {}
        else:
            new_data = {
                'Department': data['department'],
                'Unit': data['unit'],
                'Name': data['name'].strip().title(),
                'Employee_No': employee.strip(),
                'Assessments': {test_name: {
                    ver:{}
                }}
            }

        reply = {
            'pass': True if data.get('pass-fail','') == 'pass' else False,
        }

        result = {}
        for key, value in data.items():

            # Deal with questions
            if key.startswith('q'):
                print(key)
                group, subkey = key.split('__', 1) # Use double underscore to separate question id and option id
                if group not in result:
                    result[group] = {}
                result[group][subkey] = value

            # Deal with other entry fields
            if '_entry' in key:
                
                # Deal with test date
                if 'test_date' in key:
                    reply['time'] = value
                    result[key.replace('test_date_','')] = value

                else:
                    which_entry = "_".join(key.split("_")[:-1])

                    # Deal with names
                    if '名' in the_test['content'][which_entry]['details'][key]['title']:
                        result[key] = value.strip().title()
                    else:
                        result[key] = value.strip()



        reply['result'] = result

        if test_name not in new_data['Assessments']:
            new_data['Assessments'][test_name] = {}

        new_data['Assessments'][test_name][ver] = reply

        db.child(staff_type).child(employee).set(new_data)

        flash('Data added successfully!', 'success')


        return redirect(url_for('ui'))


@app.route('/pca-checklists', methods=['GET', 'POST'])
def pca_checklists():

    if 'user_id' not in session:
        return redirect(url_for('index'))
    

    tests = dict(db.child('test').get().val())
    

    if request.method == 'GET':

        allowed_departments, units_by_department = get_dept_and_ward(session)

        # pca_tests = {
        #     k: v[sorted(v.keys())[-1]]
        #     for k, v in tests.items()
        #     if k.startswith('PCA')
        #     and k != 'PCA_Fall_Test'
        # }

        pca_tests = [
            {
                'test_name': test_name,
                'title':  test[sorted(test.keys())[-1]]['content']['intro']['header'],
            }
            for test_name, test in tests.items()
            if test_name.startswith('PCA')
            and test[sorted(test.keys())[-1]]['info']['form_type'] == 'checklist'

        ]



        pcas = dict(db.child('pca').get().val())
        
        try:
            temp_staff = dict(db.child('temp_staff').get().val())
        except:
            temp_staff = {}

        if not temp_staff:
            next_temp_idx = '0001'
        else:
            next_temp_idx = f"{int(max(temp_staff.keys()).replace('TEMP','')) + 1:04d}"

        info = request.args
        tutor_name = info.get('tutor_name', '')
        tutor_rank = info.get('tutor_rank', '')
        observer_name = info.get('observer_name', '')
        observer_rank = info.get('observer_rank', '')
        assessor_name = info.get('assessor_name', '')
        assessor_rank = info.get('assessor_rank', '')
        pca_dept = info.get('pca_dept', '')
        pca_unit = info.get('pca_unit', '')



        return render_template(
            'pca-checklists.html',
            basic_app_config=BASIC_APP_CONFIG,
            pca_tests=pca_tests,
            allowed_departments=allowed_departments,
            units_by_department=units_by_department,
            ranks=RANKS,
            pcas=pcas,
            temp_staff=temp_staff,
            tutor_name=tutor_name,
            tutor_rank=tutor_rank,
            observer_name=observer_name,
            observer_rank=observer_rank,
            assessor_name=assessor_name,
            assessor_rank=assessor_rank,
            pca_dept=pca_dept,
            pca_unit=pca_unit,
            next_temp_idx=next_temp_idx
        )


    if request.method == 'POST':

        data = request.form

        info = {
            'tutor_name' : data.get('tutor_name', '').strip().title(),
            'tutor_rank' : data.get('tutor_rank', ''),
            'tutor_date' : data.get('tutor_date', ''),
            'observer_name' : data.get('observer_name', '').strip().title(),
            'observer_rank' : data.get('observer_rank', ''),
            'observer_date' : data.get('observer_date', ''),
            'assessor_name' : data.get('assessor_name', ''.strip().title()),
            'assessor_rank' : data.get('assessor_rank', ''),
            'assessor_date' : data.get('assessor_date', ''),
            'pca_dept' : data.get('pca_dept', ''),
            'pca_unit' : data.get('pca_unit', ''),
        }

        print(info)

        pca_name = data.get('pca_name', '').strip().title()
        test_date = data.get('assessor_date', '')
        if 'new_temp_staff' in data.keys():
            pca_employeeno = data.get('new_temp_staff_no', '').strip()
        else:
            pca_employeeno = data.get('pca_employeeno', '').strip()


        tests_passed = [
            test_name
            for test, test_name in data.items()
            if test.startswith('checklist_')
        ]

        # print(tests_passed)

        if 'new_temp_staff' in data.keys() or 'old_temp_staff' in data.keys():
            folder = 'temp_staff'
        else:
            folder = 'pca'

        staff = db.child(folder).get().val()
        if staff is None:
            staff = {}

        if pca_employeeno not in staff.keys():
            new_data = {
                'Department': info['pca_dept'],
                'Unit': info['pca_unit'],
                'Name': pca_name,
                'Employee_No': pca_employeeno,
                'Assessments': {}
            }

        else:
            new_data = staff[pca_employeeno]
            if 'Assessments' not in new_data:
                new_data['Assessments'] = {}


        for test in tests_passed:


            version = sorted(tests[test].keys())[-1]

            new_data['Assessments'][test] = {
                version: {
                    'result': {},
                    'pass': True,
                    'time': test_date
                },
            }


            for part, content in tests[test][version]['content'].items():

                if part == 'additional_entry' or part == 'basic_entry':

                    for entry, context in content['details'].items():
                        for organizer, organizer_name in ORGANIZER_NAMES.items():
                            if context['title'] == organizer_name:
                                new_data['Assessments'][test][version]['result'][entry] = info[f"{organizer}_name"]

                                # check if the the next item's input is 'rank', is so, add the rank to the result
                                next_key = list(content['details'].keys())[list(content['details'].keys()).index(entry)+1]
                                next_next_key = list(content['details'].keys())[list(content['details'].keys()).index(entry)+2]
                                if content['details'][next_key]['input'] == 'rank':
                                    new_data['Assessments'][test][version]['result'][next_key] = info[f"{organizer}_rank"]
                                if content['details'][next_next_key]['input'] in ['date', 'test_date']:
                                    new_data['Assessments'][test][version]['result'][next_next_key] = info[f"{organizer}_date"]
                                break
                            elif context['title'] == '總評分等級':
                                new_data['Assessments'][test][version]['result'][entry] = '滿意'
                            elif entry not in new_data['Assessments'][test][version]['result'].keys():
                                new_data['Assessments'][test][version]['result'][entry] = ''

                elif part == 'questions':

                    for q, item in content['details']['items']:
                        if item.get('subtitle', '') == 1 or item.get('subtitle', '') == 2:
                            continue
                        new_data['Assessments'][test][version]['result'][q] = { 'o01': 'true'}

        
        db.child(folder).child(pca_employeeno).set(new_data)

        flash('Data added successfully!', 'success')

        return redirect(url_for(
            'pca_checklists',
            **info
        ))


@app.route('/quiz-result')
def quiz_result():

    re_test_name = request.args.get('re_test_name', '')
    score = request.args.get('score', '')
    quiz_pass = request.args.get('quiz_pass', '')

    return render_template(
        'quiz-result.html',
        basic_app_config=BASIC_APP_CONFIG,
        re_test_name=re_test_name,
        score=score,
        quiz_pass=quiz_pass
    )


@app.route('/transfer',methods=['GET','POST'])
def transfer():
    if 'user_id' not in session:
        return redirect(url_for('index'))
    
    try:
        operator_name = dict(db.child('operator').get().val())
    except:
        return "Database access failure. Please restart and try again."
    
    departments, units_by_department = get_all_depts_units()
    
    if request.method == 'GET':

        return render_template(
            'transfer.html',
            basic_app_config=BASIC_APP_CONFIG,
            departments=departments, 
            units_by_department=units_by_department,
            operator_name=operator_name
        )
    
    
    if request.method == 'POST':

        corp_id = request.form.get('corp_id','')
        dept = request.form.get('department','')
        unit = request.form.get('unit','')

        if not corp_id or not dept or not unit:
            return redirect(url_for('transfer'))
        
        db.child('operator').child(corp_id).update({'Department':dept,'Unit':unit})
        flash('Transfer successful!','success')

        return render_template(
            'transfer.html',
            basic_app_config=BASIC_APP_CONFIG,
            departments=departments, 
            units_by_department=units_by_department,
            operator_name=operator_name
        )


@app.route('/assessor', methods=['GET', 'POST'])
def assessor():
    if 'user_id' not in session:
        return redirect(url_for('index'))

    allowed_departments, units_by_department = get_dept_and_ward(session)


    units_by_department = {
        key:['ALL'] + value 
        for key, value in units_by_department.items()
    }
    
    return render_template(
        'assessor.html',
        basic_app_config=BASIC_APP_CONFIG,
        allowed_departments=allowed_departments, 
        units_by_department=units_by_department
    )

@app.route('/admin', methods=['GET', 'POST'])
def admin():
    if 'user_id' not in session or session['user_email'] not in [data['email'] for data in ADMIN_PERMISSION]:
        return redirect(url_for('index'))
    
    tests_overview = get_tests_overview()

    if request.method == 'POST':
        # Get the form data
        data = request.form.to_dict()
        print(data)

        if 'new_test' in data:
            
            if data['test-name'] == 'new':
                
                test_name = data['new-test-name'].strip().replace(' ','_')
                
                if data['test-type'] == 'mc':
                    return redirect(
                        url_for(
                            'new_mc',
                            test_name=test_name
                        )
                    )

                if data['test-type'] == 'checklist':                
                    return redirect(
                        url_for(
                            'new_checklist',
                            test_name=test_name
                        )
                    )
            
            else:

                if data['test-type'] == 'mc':
                    return redirect(
                        url_for(
                            'edit_mc',
                            **data
                        )
                    )
                
                if data['test-type'] == 'checklist':
                    return redirect(
                        url_for(
                            'edit_checklist',
                            **data
                        )
                    )

              
    
    return render_template(
        'admin.html',
        basic_app_config=BASIC_APP_CONFIG,
        tests_overview=tests_overview
    )


@app.route('/admin/delete-operator', methods=['GET', 'POST'])
def delete_operator():

    if 'user_id' not in session or session['user_email'] not in [data['email'] for data in ADMIN_PERMISSION]:
        return redirect(url_for('index'))
    
    try:
        operator_name = dict(db.child('operator').get().val())
    except:
        return "Database access failure. Please restart and try again."
    
    if request.method == 'POST':
        # Get the form data
        data = request.form

        # Delete the data from the database
        db.child("operator").child(data['corp_id']).remove()

        # Flash a success message
        flash('Data deleted successfully!', 'success')

        # Redirect back to the same page with the department, unit, startdate, and expirydate values retained
        return render_template(
            'delete-operator.html',
            basic_app_config=BASIC_APP_CONFIG,
            operator_name=operator_name
        )

    
    
    return render_template(
        'delete-operator.html',
        basic_app_config=BASIC_APP_CONFIG,
        operator_name=operator_name
    )

@app.route('/edit-unit', methods=['GET', 'POST'])
def edit_unit():
    if 'user_id' not in session:
        return redirect(url_for('index'))

    if request.method == 'POST':
        # Get the form data
        data = request.form

        print(data)

        if 'submit-a' in data:
            # Add the data to the database
            if data['a-department'] == '[ ADD NEW ]':
                dept_to_add = data['dept_other']
            else:
                dept_to_add = data['a-department']

            new_access = {
                'dept': dept_to_add,
                'unit': data['a-unit'],
                'email': data['a-email'],
                'access': [item.strip() for item in data['add-access'].split(',')]
            }

            if 'invisible-to-public' in data:
                new_access['invisible'] = True

            try:
                db.child("permission").child(data['a-unit']).set(new_access)

                pyre_auth.create_user_with_email_and_password(data['a-email'], data['a-password'])

                flash('Data added successfully!', 'success')

            except:
                flash('Failed adding data', 'danger')


            return redirect(url_for('edit_unit'))
        
        if 'submit-e' in data:
            # Edit the data in the database
            new_access = {
                'access': [item.strip() for item in data['edit-access'].split(',')],
            }
            if 'e-invisible' in data:
                new_access['invisible'] = True
            else:
                new_access['invisible'] = None

            try:
                db.child("permission").child(data['e-unit']).update(new_access)
                flash('Data edited successfully!', 'success')
            except:
                flash('Failed editing data', 'danger')
            
            return redirect(url_for('edit_unit'))
        
    allowed_departments, units_by_department = get_dept_and_ward(session, show_invisible=True)

    allowed_departments =  allowed_departments + ['[ ADD NEW ]']

    form_status = {
        'a': ['active','show active'],
        'e': ['','']
    }

    return render_template(
        'edit-unit.html',
        basic_app_config=BASIC_APP_CONFIG,
        allowed_departments=allowed_departments, 
        units_by_department=units_by_department,
        form_status=form_status
    )





@app.route('/change-password',methods=['GET','POST'])
def change_pw():
    if 'user_id' not in session:
        return redirect(url_for('index'))
    
    if request.method == 'POST':

        new_pw = request.form.get('newPW')

        if len(new_pw) < 6:
            flash('Password must be at least 6 characters.','danger')
            return redirect(url_for('change_pw'))
        
        cred = credentials.Certificate(cred_file_path)
        fb.initialize_app(cred)
        fb_user = fire_auth.update_user(
            session['user_id'],
            password=new_pw
        )
        fb.delete_app(fb.get_app())
        flash('Password changed, please re-authenticate.','success')

        return redirect(url_for('logout'))
    
    return render_template('change-pw.html',basic_app_config=BASIC_APP_CONFIG)

@app.route('/add-staff',methods=['GET','POST'])
def add_staff():
    if 'user_id' not in session:
        return redirect(url_for('index'))
    
    nurses = dict(db.child('nurse').get().val() if db.child('nurse').get().val() else {})
    pcas = dict(db.child('pca').get().val() if db.child('pca').get().val() else {})
    temp_staff = dict(db.child('temp_staff').get().val() if db.child('temp_staff').get().val() else {})

    all_staff = {
        'nurse': nurses,
        'pca': pcas,
    }
    
    allowed_departments, units_by_department = get_dept_and_ward(session)

    
    if request.method == 'POST':
        data = request.form
        if data['update'] == 'false':
            new_staff = {
                'Department': data['employee_department'],
                'Unit': data['employee_unit'],
                'Name': data['employee_name'].strip().title(),
                'Employee_No': data['employee_no'].strip(),
            }
        else:

            if 'from_temp' in data:
                new_staff = temp_staff[data['temp_staff']]
            else:
                new_staff = all_staff[data['employee_discipline']][data['employee_no']]

            new_staff['Employee_No'] = data['employee_no'].strip()
            new_staff['Name'] = data['employee_name'].strip().title()
            new_staff['Department'] = data['employee_department']
            new_staff['Unit'] = data['employee_unit']

        all_staff[data['employee_discipline']][new_staff['Employee_No']] = new_staff

        db.child(data['employee_discipline']).set(all_staff[data['employee_discipline']])

        if 'from_temp' in data:
            db.child('temp_staff').child(data['temp_staff']).remove()
        

        

        return redirect(url_for('add_staff'))
    
    return render_template(
        'add-staff.html',
        basic_app_config=BASIC_APP_CONFIG,
        allowed_departments=allowed_departments,
        units_by_department=units_by_department,
        all_staff=all_staff,
        temp_staff=temp_staff
    )

@app.route('/staff-info', methods=['GET', 'POST'])
def staff_info():
    if 'user_id' not in session:
        return redirect(url_for('index'))

    if request.method == 'POST':
        employee_number = request.form.get('employee_number', '').strip()

        # If this is the initial form submission with employee number
        if 'employee_number' in request.form and 'name' not in request.form:
            if not employee_number or len(employee_number) != 6 or not employee_number.isdigit():
                flash('Please enter a valid 6-digit employee number.', 'danger')
                return redirect(url_for('ui'))

            # Search for staff in all databases
            staff_data = None
            staff_type = None

            # Check nurse database
            nurses = dict(db.child('nurse').get().val() if db.child('nurse').get().val() else {})
            if employee_number in nurses:
                staff_data = nurses[employee_number]
                staff_type = 'nurse'

            # Check PCA database if not found in nurses
            if not staff_data:
                pcas = dict(db.child('pca').get().val() if db.child('pca').get().val() else {})
                if employee_number in pcas:
                    staff_data = pcas[employee_number]
                    staff_type = 'pca'

            # Check temp_staff database if not found
            if not staff_data:
                temp_staff = dict(db.child('temp_staff').get().val() if db.child('temp_staff').get().val() else {})
                if employee_number in temp_staff:
                    staff_data = temp_staff[employee_number]
                    staff_type = 'temp_staff'

            if not staff_data:
                flash(f'No staff found with employee number: {employee_number}', 'danger')
                return redirect(url_for('ui'))

            # Get departments and units for the form
            allowed_departments, units_by_department = get_all_depts_units()

            return render_template(
                'staff-info.html',
                basic_app_config=BASIC_APP_CONFIG,
                staff_data=staff_data,
                staff_type=staff_type,
                employee_number=employee_number,
                allowed_departments=allowed_departments,
                units_by_department=units_by_department
            )

        # If this is the update form submission
        else:
            name = request.form.get('name', '').strip().title()
            department = request.form.get('department', '').strip()
            unit = request.form.get('unit', '').strip()
            staff_type = request.form.get('staff_type', '').strip()

            if not all([employee_number, name, department, unit, staff_type]):
                flash('All fields are required.', 'danger')
                return redirect(url_for('ui'))

            # Validate name (should not be empty after stripping)
            if not name:
                flash('Name cannot be empty.', 'danger')
                return redirect(url_for('ui'))

            # Update the staff data
            updated_data = {
                'Name': name,
                'Employee_No': employee_number,
                'Department': department,
                'Unit': unit
            }

            # Get existing data to preserve assessments
            existing_staff = dict(db.child(staff_type).get().val() if db.child(staff_type).get().val() else {})
            if employee_number in existing_staff:
                # Preserve existing assessments
                if 'Assessments' in existing_staff[employee_number]:
                    updated_data['Assessments'] = existing_staff[employee_number]['Assessments']

            # Update the database
            db.child(staff_type).child(employee_number).set(updated_data)

            flash(f'Staff information updated successfully for {name} ({employee_number}).', 'success')
            return redirect(url_for('ui'))

    # GET request - redirect to main page
    return redirect(url_for('ui'))


@app.route('/logout')
def logout():
    if 'user_id' in session:
        session.pop('user_id', None)
    return redirect(url_for('index'))





