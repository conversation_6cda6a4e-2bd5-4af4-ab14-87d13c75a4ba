{% extends 'base.html' %} 

{% block custom_head %}
<style>
  .pale-orange {
    background-color: #ffd8b8;
  }

  .pale-yellow {
    background-color: #fafab8;
  }
</style>
{% endblock %}

{% block content %}

<div class="container mt-5">
  <h1 class="mb-5">Dashboard</h1>
  <p class="lead">Welcome, {{ user_name }}</p>
  <div class="row">
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <h5 class="card-title">View</h5>
          <p class="card-text">View Staff</p>
        </div>
        <div class="card-footer">
          <a href="{{ url_for('view') }}" class="btn btn-success">Go to View</a>
        </div>
      </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <h5 class="card-title">Account Management</h5>
          <p class="card-text">Change your account password.</p>
        </div>
        <div class="card-footer">
          <a href="{{ url_for('change_pw') }}" class="btn btn-success"
            >Change Password</a
          >
        </div>
      </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <h5 class="card-title">Add Staff</h5>
          <p class="card-text">Register new staff profile</p>
        </div>
        <div class="card-footer">
          <a href="{{ url_for('add_staff') }}" class="btn btn-success"
            >Go to Add</a
          >
        </div>
      </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body">
          <h5 class="card-title">Staff Info</h5>
          <p class="card-text">Change staff info</p>
        </div>
        <div class="card-footer">
          <button
            type="button"
            class="btn btn-success"
            data-bs-toggle="modal"
            data-bs-target="#staffInfoModal"
          >Go to Staff</button>
        </div>
      </div>
    </div>

  </div>

  <hr/>

  <div class="row">

    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body pale-orange">
          <h5 class="card-title">Fall Quiz (Nurse)</h5>
          <p class="card-text">Do Quiz</p>
        </div>
        <div class="card-footer">
          <a 
            href="{{ url_for('fall_quiz_form') }}" 
            class="btn btn-warning"
          >Go to Quiz</a>
        </div>
      </div>
    </div>
  
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body pale-orange">
          <h5 class="card-title">AED123</h5>
          <p class="card-text">Do Checklist</p>
        </div>
        <div class="card-footer">
          <a 
            href="{{ url_for('checklist_form', test_name='AED123') }}" 
            class="btn btn-warning"
          >Go to Checklist</a>
        </div>
      </div>
    </div>


    <hr/>

    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body pale-yellow">
          <h5 class="card-title">Fall Quiz (PCA)</h5>
          <p class="card-text">Do Quiz</p>
        </div>
        <div class="card-footer">
          <a 
            href="{{ url_for('pca_fall_quiz_form') }}" 
            class="btn btn-warning"
          >Go to Quiz</a>
        </div>
      </div>
    </div>



    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body pale-yellow">
          <h5 class="card-title">PCA Checklists</h5>
          <p class="card-text">Bulk Process</p>
        </div>
        <div class="card-footer">
          <a 
            href="{{ url_for('pca_checklists') }}" 
            class="btn btn-warning"
          >Go to Checklist</a>
        </div>
      </div>
    </div>


  </div>
</div>

<!-- Staff Info Modal -->
<div class="modal fade" id="staffInfoModal" tabindex="-1" aria-labelledby="staffInfoModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="staffInfoModalLabel">Enter Employee Number</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="staffInfoForm" method="POST" action="{{ url_for('staff_info') }}">
        <div class="modal-body">
          <div class="mb-3">
            <label for="employeeNumber" class="form-label">Employee Number (6 digits):</label>
            <input
              type="text"
              class="form-control"
              id="employeeNumber"
              name="employee_number"
              pattern="[0-9]{6}"
              maxlength="6"
              required
              placeholder="Enter 6-digit employee number"
            >
            <div class="form-text">Please enter a 6-digit employee number.</div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Submit</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>

  // Validate employee number input
  document.getElementById('employeeNumber').addEventListener('input', function(e) {
    // Only allow digits
    e.target.value = e.target.value.replace(/[^0-9]/g, '');

    // Limit to 6 digits
    if (e.target.value.length > 6) {
      e.target.value = e.target.value.slice(0, 6);
    }
  });
</script>

{% endblock %}
