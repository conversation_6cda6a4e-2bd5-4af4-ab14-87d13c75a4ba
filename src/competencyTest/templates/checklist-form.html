{% extends 'base.html' %} 

{% block custom_head %}

<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

{% endblock %}

{% block content %}
<style>
  .card-header {
    background-color: #c9e1bf; /* Replace with the desired background color */
  }

  .question-text {
    color: #1e159d;
  }

  table,textarea {
    width: 100%;
  }

  .item-content,label {
    white-space: pre-wrap;
  }

  #item-col {
    min-width: 400px;
    width: 60%;
  }

  .h-center {
    text-align: center;
  }

  .v-center {
    vertical-align: middle;
  }

  .bold-large {
    font-weight: bold;
    font-size: large;
  }

</style>

<h1>{{ test_name|replace("_", " ") }}</h1>
<h1>{{ checklist_content.get('intro', {}).get('header', '') }}</h1>
{% if checklist_info.version_name %}
<h5 style="color: #9d9d9d">({{ checklist_info.version }} - {{ checklist_info.version_name }})</h5>
{% else %}
<h5 style="color: #9d9d9d">({{ checklist_info.version }})</h5>
{% endif %}
<div class="container-fluid mx-1">
  <form id="complete-form" action="" method="post">

    <input type="hidden" name="test_name" value="{{ test_name }}">
    <input type="hidden" name="test_version" value="{{ checklist_info.version }}">

    <div class="card mt-5">
      <div class="card-header">
        <h3>Staff Info</h3>
      </div>
      <div class="card-body">
        <div class="form-group mt-3">
          <label for="department">Department:</label>
          <select class="form-select" name="department" id="department">
            {% for dept in departments %}
            <option value="{{ dept }}">{{ dept }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="form-group mt-3">
          <label for="unit">Unit:</label>
          <select class="form-select" name="unit" id="unit">
            <!-- Unit options will be populated using JavaScript -->
          </select>
        </div>
        <div class="form-group mt-3">
          <label for="name">Name:</label>
          <input
            class="form-control"
            type="text"
            name="name"
            id="name"
            required
          />
        </div>
        <div class="form-group mt-3">
          <label for="corpid">Employee No:</label>
          <input
            class="form-control"
            type="number"
            name="employeeno"
            id="employeeno"
            required
          />
        </div>
      </div>
    </div>

    <!-- Dynamic form content will be generated here by JavaScript -->
    <div id="dynamic-form-content">
      <!-- Form sections will be populated by utils.js generateChecklistFormContent() -->
    </div>

    <button type="submit" id="form-submit" class="btn btn-success mt-5 mb-5">Submit</button>

  </form>
</div>


<button type="button" id="trigger-modal" class="btn btn-primary" data-toggle="modal" data-target="#confirm-staff-modal" hidden></button>
<div class="modal fade" id="confirm-staff-modal" tabindex="-1" role="dialog" aria-labelledby="confirm-syaff-modal-label" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="confirm-staff-modal-label">Confirm Staff</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <h1 class="mb-3" id="found-staff">Found Existing Staff</h1>
        <div class="mb-3">
          <label for="modal-staff-employeeno" class="form-label">Employee No.:</label>
          <input type="text" class="form-control" id="modal-staff-employeeno" value="" readonly>
        </div>
        <div class="mb-3">
          <label for="modal-staff-name" class="form-label">Name:</label>
          <input type="text" class="form-control" id="modal-staff-name" value="" readonly>
        </div>
        <div class="mb-3">
          <label for="modal-staff-dept" class="form-label">Department:</label>
          <input type="text" class="form-control" id="modal-staff-dept" value="" readonly>
        </div>
        <div class="mb-3">
          <label for="modal-staff-unit" class="form-label">Unit:</label>
          <input type="text" class="form-control" id="modal-staff-unit" value="" readonly>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="confirm-staff-btn">Confirm</button>
      </div>
    </div>
  </div>
</div>

<!-- Add a container with data attributes -->
<div
  id="data-container"
  data-pcas='{{ pcas | tojson | safe }}'
  data-temp-staff='{{ temp_staff | tojson | safe }}'
  data-nurses='{{ nurses | tojson | safe }}'
  data-units-by-department='{{ units_by_department | tojson | safe }}'
  data-passing-criteria='{{ passing_criteria | tojson | safe }}'
  data-checklist-info='{{ checklist_info | tojson | safe }}'
  data-checklist-content='{{ checklist_content | tojson | safe }}'
  data-departments='{{ departments | tojson | safe }}'
  data-ranks='{{ ranks | tojson | safe }}'
  data-is-pca-assessment-form="{{ is_pca_assessment_form }}"
></div>

<script type="module" src="{{ url_for('static', filename='js/checklist_form.js') }}"></script>

<script>

{# $(document).ready(function() {
    $('.with-search').select2();
}); #}

</script>



{% endblock %}
