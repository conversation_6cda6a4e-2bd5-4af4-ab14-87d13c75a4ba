{% extends 'base.html' %} 

{% block custom_head %}
<style>
  .readonly-field {
    background-color: #f8f9fa;
    cursor: not-allowed;
  }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header">
          <h4 class="mb-0">Staff Information</h4>
          <small class="text-muted">Update staff name, department and unit information</small>
        </div>
        <div class="card-body">
          <form method="POST" action="{{ url_for('staff_info') }}">
            <!-- Hidden fields -->
            <input type="hidden" name="employee_number" value="{{ employee_number }}">
            <input type="hidden" name="staff_type" value="{{ staff_type }}">
            
            <!-- Name field (editable) -->
            <div class="mb-3">
              <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
              <input
                type="text"
                class="form-control"
                id="name"
                name="name"
                value="{{ staff_data.Name }}"
                required
              >
              <div class="form-text">Enter the staff member's full name.</div>
            </div>
            
            <!-- Employee Number field (disabled) -->
            <div class="mb-3">
              <label for="employee_no" class="form-label">Employee Number</label>
              <input 
                type="text" 
                class="form-control readonly-field" 
                id="employee_no" 
                value="{{ staff_data.Employee_No }}" 
                readonly
              >
              <div class="form-text">Employee number cannot be modified.</div>
            </div>
            
            <!-- Department field (select) -->
            <div class="mb-3">
              <label for="department" class="form-label">Department <span class="text-danger">*</span></label>
              <select class="form-select" id="department" name="department" required>
                <option value="">Select Department</option>
                {% for dept in allowed_departments %}
                  <option value="{{ dept }}" {% if dept == staff_data.Department %}selected{% endif %}>
                    {{ dept }}
                  </option>
                {% endfor %}
              </select>
            </div>
            
            <!-- Unit field (select) -->
            <div class="mb-3">
              <label for="unit" class="form-label">Unit <span class="text-danger">*</span></label>
              <select class="form-select" id="unit" name="unit" required>
                <option value="">Select Unit</option>
                <!-- Options will be populated by JavaScript -->
              </select>
            </div>
            
            <!-- Staff Type Display -->
            <div class="mb-3">
              <label class="form-label">Staff Type</label>
              <div class="form-control readonly-field">
                {% if staff_type == 'nurse' %}
                  <span class="badge bg-success">Nurse</span>
                {% elif staff_type == 'pca' %}
                  <span class="badge bg-warning">PCA</span>
                {% elif staff_type == 'temp_staff' %}
                  <span class="badge bg-info">Temporary Staff</span>
                {% endif %}
              </div>
            </div>
            
            <!-- Original Information Display -->
            <div class="alert alert-info">
              <h6>Original Information:</h6>
              <p class="mb-1"><strong>Name:</strong> {{ staff_data.Name }}</p>
              <p class="mb-1"><strong>Employee No:</strong> {{ staff_data.Employee_No }}</p>
              <p class="mb-1"><strong>Department:</strong> {{ staff_data.Department }}</p>
              <p class="mb-0"><strong>Unit:</strong> {{ staff_data.Unit }}</p>
            </div>
            
            <!-- Submit buttons -->
            <div class="d-flex justify-content-between">
              <button type="button" class="btn btn-secondary" onclick="location.href='{{ url_for('ui') }}';">
                Cancel
              </button>
              <button type="submit" class="btn btn-primary">
                Update Staff Information
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-warning text-dark">
        <h5 class="modal-title" id="confirmationModalLabel">
          <i class="fas fa-exclamation-triangle me-2"></i>Confirm Staff Information Update
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Are you sure you want to update the staff information with the following details?</p>

        <div class="card">
          <div class="card-body">
            <div class="row mb-2">
              <div class="col-4"><strong>Name:</strong></div>
              <div class="col-8" id="confirmName"></div>
            </div>
            <div class="row mb-2">
              <div class="col-4"><strong>Department:</strong></div>
              <div class="col-8" id="confirmDepartment"></div>
            </div>
            <div class="row mb-0">
              <div class="col-4"><strong>Unit:</strong></div>
              <div class="col-8" id="confirmUnit"></div>
            </div>
          </div>
        </div>

        <div class="alert alert-info mt-3 mb-0">
          <small><i class="fas fa-info-circle me-1"></i>This action will update the staff member's information in the database.</small>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="fas fa-times me-1"></i>Cancel
        </button>
        <button type="button" class="btn btn-primary" onclick="confirmUpdate()">
          <i class="fas fa-check me-1"></i>Confirm Update
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Data container for JavaScript -->
<div
  id="data-container"
  data-units-by-department='{{ units_by_department | tojson | safe }}'
  data-current-unit='{{ staff_data.Unit }}'
></div>

<script type="module" src="{{ url_for('static', filename='js/staff_info.js') }}"></script>

{% endblock %}
